"""
SoftREPA-style sampler for TransformerForDiffusion_DC in robot action diffusion.
Based on SoftREPA's contrastive learning approach for improving action-observation alignment.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional, Tuple, Dict, Any
import numpy as np
from dataclasses import dataclass

from lerobot.common.policies.diffusion.modeling_diffusion import (
    TransformerForDiffusion_DC,
    DiffusionConfig
)


@dataclass
class TransformerDCSamplerConfig:
    """Configuration for TransformerDC sampler"""
    n_dc_tokens: int = 4
    n_dc_layers: int = 6
    use_dc_t: bool = True
    temp: float = 0.07
    scale: float = 4.0
    dweight: float = 0.0  # diffusion loss weight
    num_train_timesteps: int = 1000
    device: str = 'cuda'
    dtype: str = 'float32'


class TransformerDCSampler(nn.Module):
    """
    SoftREPA-style sampler for robot action diffusion using TransformerForDiffusion_DC.
    
    This sampler implements contrastive learning for action-observation alignment,
    similar to SoftREPA's approach but adapted for robot action sequences.
    """
    
    def __init__(
        self, 
        config: DiffusionConfig,
        sampler_config: TransformerDCSamplerConfig,
        cond_dim: int
    ):
        super().__init__()
        self.config = config
        self.sampler_config = sampler_config
        self.cond_dim = cond_dim
        self.device = sampler_config.device
        self.dtype = getattr(torch, sampler_config.dtype)
        
        # Create the denoiser with DC tokens
        self.denoiser = TransformerForDiffusion_DC(
            config=config,
            cond_dim=cond_dim,
            n_dc_tokens=sampler_config.n_dc_tokens,
            n_dc_layers=sampler_config.n_dc_layers,
            use_dc_t=sampler_config.use_dc_t,
            use_dc=True  # Enable DC tokens
        )
        
        # Initialize noise scheduler (simplified linear schedule)
        self.num_train_timesteps = sampler_config.num_train_timesteps
        self.betas = torch.linspace(0.0001, 0.02, sampler_config.num_train_timesteps)
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        
        # Pre-generated noise for efficiency
        self.all_noise = None
        self.noise_batch_size = 1
        
        # Move to device
        self.to(self.device)
        
    def set_noise(self, action_shape: Tuple[int, ...], batch_size: int = 1):
        """Pre-generate noise for efficient training"""
        self.noise_batch_size = batch_size
        self.all_noise = torch.randn(
            batch_size, *action_shape, 
            device=self.device, 
            dtype=self.dtype
        )
    
    def add_noise(self, actions: torch.Tensor, noise: torch.Tensor, timesteps: torch.Tensor) -> torch.Tensor:
        """
        Add noise to actions according to DDPM forward process.
        
        Args:
            actions: (B, T, action_dim) clean action sequences
            noise: (B, T, action_dim) noise to add
            timesteps: (B,) timestep indices
            
        Returns:
            (B, T, action_dim) noisy actions
        """
        # Get noise schedule parameters
        sqrt_alpha_prod = torch.sqrt(self.alphas_cumprod[timesteps]).to(self.device)
        sqrt_one_minus_alpha_prod = torch.sqrt(1 - self.alphas_cumprod[timesteps]).to(self.device)
        
        # Reshape for broadcasting: (B,) -> (B, 1, 1)
        sqrt_alpha_prod = sqrt_alpha_prod.view(-1, 1, 1)
        sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.view(-1, 1, 1)
        
        # DDPM forward process: x_t = sqrt(α_t) * x_0 + sqrt(1-α_t) * ε
        noisy_actions = sqrt_alpha_prod * actions + sqrt_one_minus_alpha_prod * noise
        
        return noisy_actions
    
    def predict_noise(
        self, 
        noisy_actions: torch.Tensor, 
        timesteps: torch.Tensor, 
        conditions: torch.Tensor,
        use_dc: bool = False
    ) -> torch.Tensor:
        """
        Predict noise using the denoiser.
        
        Args:
            noisy_actions: (B, T, action_dim) noisy action sequences
            timesteps: (B,) timestep indices
            conditions: (B, cond_dim) observation conditions
            use_dc: whether to use DC tokens
            
        Returns:
            (B, T, action_dim) predicted noise
        """
        # Set DC usage in the model
        self.denoiser.use_dc = use_dc
        
        # Forward pass through transformer
        pred_noise = self.denoiser(
            sample=noisy_actions,
            timestep=timesteps,
            global_cond=conditions
        )
        
        return pred_noise
    
    def compute_error(
        self,
        actions: torch.Tensor,
        conditions: torch.Tensor,
        timesteps: torch.Tensor,
        noise: Optional[torch.Tensor] = None,
        use_dc: bool = False
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compute prediction error for contrastive learning.
        
        Args:
            actions: (B, T, action_dim) clean action sequences
            conditions: (B, cond_dim) observation conditions  
            timesteps: (B,) timestep indices
            noise: (B, T, action_dim) noise to add (optional)
            use_dc: whether to use DC tokens
            
        Returns:
            Tuple of (true_noise, predicted_noise)
        """
        if noise is None:
            noise = torch.randn_like(actions, device=self.device, dtype=self.dtype)
        
        # Add noise to actions
        noisy_actions = self.add_noise(actions, noise, timesteps)
        
        # Predict noise
        pred_noise = self.predict_noise(noisy_actions, timesteps, conditions, use_dc=use_dc)
        
        return noise, pred_noise
    
    def initialize_dc(self, dc_tokens: torch.Tensor, dc_t_tokens: Optional[torch.Tensor] = None):
        """Initialize DC tokens from pretrained weights (for transfer learning)"""
        self.denoiser.dc_tokens.data = dc_tokens.type(self.denoiser.dc_tokens.data.dtype)
        if dc_t_tokens is not None and self.denoiser.use_dc_t:
            self.denoiser.dc_t_tokens.weight.data = dc_t_tokens.type(self.denoiser.dc_tokens.data.dtype)
    
    def freeze_base_model(self):
        """Freeze all parameters except DC tokens"""
        for name, param in self.denoiser.named_parameters():
            if 'dc' in name:
                param.requires_grad = True
            else:
                param.requires_grad = False
    
    def get_trainable_parameters(self) -> List[Dict[str, Any]]:
        """Get only DC-related parameters for optimization"""
        trainable_params = []
        for name, param in self.denoiser.named_parameters():
            if 'dc' in name and param.requires_grad:
                trainable_params.append({'params': param, 'name': name})
        return trainable_params


class ContrastiveLoss(nn.Module):
    """
    Contrastive loss for action-observation alignment.
    Adapted from SoftREPA for robot action sequences.
    """
    
    def __init__(self, temp: float = 0.07, scale: float = 4.0, dweight: float = 0.0, device: str = 'cuda'):
        super().__init__()
        self.device = device
        self.temp = nn.Parameter(torch.tensor(temp, device=device))
        self.scale = nn.Parameter(torch.tensor(scale, device=device))
        self.dweight = dweight  # diffusion loss weight
    
    def get_mask(self, shape: Tuple[int, int]) -> torch.Tensor:
        """Create positive sample mask (diagonal)"""
        mask = torch.zeros(shape, device=self.device)
        n_b, n_p = shape
        index = torch.arange(n_b, device=self.device)
        mask[index, index] = 1
        return mask
    
    def forward(self, errors: torch.Tensor) -> torch.Tensor:
        """
        Compute contrastive loss from prediction errors.
        
        Args:
            errors: (B, B) error matrix where errors[i,j] is the error 
                   between action_i and condition_j
                   
        Returns:
            Scalar contrastive loss
        """
        # Create positive sample mask
        masks = self.get_mask(shape=errors.shape)
        
        # Convert errors to similarity scores (lower error = higher similarity)
        logits = self.scale * torch.exp(-errors / self.temp)
        
        # Cross-entropy loss for contrastive learning
        loss = F.cross_entropy(logits, masks)
        
        # Optional: add diffusion loss weight for diagonal elements
        if self.dweight > 0:
            diagonal_errors = errors[torch.arange(masks.shape[0]), torch.arange(masks.shape[0])]
            loss += self.dweight * diagonal_errors.mean()
        
        return loss


class SoftREPATrainer(nn.Module):
    """
    SoftREPA-style trainer for robot action diffusion.
    Implements contrastive learning for action-observation alignment.
    """
    
    def __init__(self, sampler: TransformerDCSampler):
        super().__init__()
        self.sampler = sampler
        self.device = sampler.device
        self.dtype = sampler.dtype
    
    def forward(
        self, 
        actions: torch.Tensor, 
        conditions: torch.Tensor, 
        timesteps: torch.Tensor,
        use_dc: bool = True
    ) -> torch.Tensor:
        """
        Forward pass for contrastive learning.
        
        Args:
            actions: (B, T, action_dim) action sequences
            conditions: (B, cond_dim) observation conditions
            timesteps: (B,) or scalar timestep(s)
            use_dc: whether to use DC tokens
            
        Returns:
            (B, B) error matrix for contrastive learning
        """
        with torch.no_grad():
            # Ensure inputs are on correct device and dtype
            actions = actions.to(device=self.device, dtype=self.dtype)
            conditions = conditions.to(device=self.device, dtype=self.dtype)
            
            if isinstance(timesteps, int):
                timesteps = torch.full((actions.shape[0],), timesteps, device=self.device)
            else:
                timesteps = timesteps.to(self.device)
        
        batch_size = actions.shape[0]
        
        # Expand for contrastive learning: each action paired with all conditions
        expanded_actions = actions.unsqueeze(1).expand(-1, batch_size, -1, -1).reshape(
            batch_size * batch_size, *actions.shape[1:]
        )  # (B*B, T, action_dim)
        
        expanded_conditions = conditions.unsqueeze(0).expand(batch_size, -1, -1).reshape(
            batch_size * batch_size, -1
        )  # (B*B, cond_dim)
        
        expanded_timesteps = timesteps.unsqueeze(1).expand(-1, batch_size).reshape(-1)  # (B*B,)
        
        # Compute prediction errors
        true_noise, pred_noise = self.sampler.compute_error(
            expanded_actions, expanded_conditions, expanded_timesteps, use_dc=use_dc
        )
        
        # Calculate MSE error for each action-condition pair
        errors = F.mse_loss(true_noise, pred_noise, reduction='none').mean(dim=(1, 2))  # (B*B,)
        
        # Reshape to contrastive matrix
        error_matrix = errors.reshape(batch_size, batch_size)  # (B, B)
        
        return error_matrix


class DDPMScheduler:
    """
    Simple DDPM scheduler for sampling.
    """

    def __init__(self, num_train_timesteps: int = 1000, beta_start: float = 0.0001, beta_end: float = 0.02):
        self.num_train_timesteps = num_train_timesteps

        # Linear beta schedule
        self.betas = torch.linspace(beta_start, beta_end, num_train_timesteps)
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = torch.cat([torch.tensor([1.0]), self.alphas_cumprod[:-1]])

        # Calculations for diffusion q(x_t | x_{t-1}) and others
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
        self.sqrt_recip_alphas = torch.sqrt(1.0 / self.alphas)

        # Calculations for posterior q(x_{t-1} | x_t, x_0)
        self.posterior_variance = self.betas * (1.0 - self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)

    def step(self, model_output: torch.Tensor, timestep: int, sample: torch.Tensor) -> torch.Tensor:
        """
        Predict the sample at the previous timestep by reversing the SDE.

        Args:
            model_output: predicted noise from the model
            timestep: current timestep
            sample: current sample x_t

        Returns:
            predicted sample at timestep t-1
        """
        t = timestep

        # 1. compute alphas, betas
        alpha_prod_t = self.alphas_cumprod[t]
        alpha_prod_t_prev = self.alphas_cumprod_prev[t] if t > 0 else torch.tensor(1.0)
        beta_prod_t = 1 - alpha_prod_t
        beta_prod_t_prev = 1 - alpha_prod_t_prev

        # 2. compute predicted original sample from predicted noise
        pred_original_sample = (sample - torch.sqrt(beta_prod_t) * model_output) / torch.sqrt(alpha_prod_t)

        # 3. compute coefficients for pred_original_sample and current sample
        pred_original_sample_coeff = torch.sqrt(alpha_prod_t_prev) * self.betas[t] / (1 - alpha_prod_t)
        current_sample_coeff = torch.sqrt(self.alphas[t]) * (1 - alpha_prod_t_prev) / (1 - alpha_prod_t)

        # 4. compute predicted previous sample
        pred_prev_sample = pred_original_sample_coeff * pred_original_sample + current_sample_coeff * sample

        return pred_prev_sample


class TransformerDCInference:
    """
    Inference engine for TransformerForDiffusion_DC with DC tokens.
    Supports both standard sampling and DC-enhanced sampling.
    """

    def __init__(self, sampler: TransformerDCSampler, num_inference_steps: int = 50):
        self.sampler = sampler
        self.scheduler = DDPMScheduler(num_train_timesteps=sampler.num_train_timesteps)
        self.num_inference_steps = num_inference_steps
        self.device = sampler.device

        # Create inference timesteps
        self.timesteps = torch.linspace(
            sampler.num_train_timesteps - 1, 0, num_inference_steps, dtype=torch.long
        )

    def sample(
        self,
        conditions: torch.Tensor,
        action_shape: Tuple[int, int],  # (T, action_dim)
        use_dc: bool = True,
        generator: Optional[torch.Generator] = None
    ) -> torch.Tensor:
        """
        Generate action sequences using DDPM sampling.

        Args:
            conditions: (B, cond_dim) observation conditions
            action_shape: (T, action_dim) shape of action sequences
            use_dc: whether to use DC tokens during sampling
            generator: random number generator for reproducibility

        Returns:
            (B, T, action_dim) generated action sequences
        """
        batch_size = conditions.shape[0]

        # Initialize with random noise
        actions = torch.randn(
            batch_size, *action_shape,
            device=self.device,
            dtype=self.sampler.dtype,
            generator=generator
        )

        # Set DC usage
        self.sampler.denoiser.use_dc = use_dc

        # Denoising loop
        for i, t in enumerate(self.timesteps):
            timestep_batch = torch.full((batch_size,), t, device=self.device, dtype=torch.long)

            # Predict noise
            with torch.no_grad():
                noise_pred = self.sampler.predict_noise(
                    actions, timestep_batch, conditions, use_dc=use_dc
                )

            # Update sample
            actions = self.scheduler.step(noise_pred, t.item(), actions)

        return actions

    def sample_with_guidance(
        self,
        conditions: torch.Tensor,
        action_shape: Tuple[int, int],
        guidance_scale: float = 1.0,
        use_dc: bool = True,
        generator: Optional[torch.Generator] = None
    ) -> torch.Tensor:
        """
        Generate action sequences with classifier-free guidance.

        Args:
            conditions: (B, cond_dim) observation conditions
            action_shape: (T, action_dim) shape of action sequences
            guidance_scale: strength of guidance (1.0 = no guidance)
            use_dc: whether to use DC tokens
            generator: random number generator

        Returns:
            (B, T, action_dim) generated action sequences
        """
        if guidance_scale == 1.0:
            return self.sample(conditions, action_shape, use_dc, generator)

        batch_size = conditions.shape[0]

        # Initialize with random noise
        actions = torch.randn(
            batch_size, *action_shape,
            device=self.device,
            dtype=self.sampler.dtype,
            generator=generator
        )

        # Create unconditional conditions (zeros)
        uncond_conditions = torch.zeros_like(conditions)

        # Set DC usage
        self.sampler.denoiser.use_dc = use_dc

        # Denoising loop with guidance
        for i, t in enumerate(self.timesteps):
            timestep_batch = torch.full((batch_size,), t, device=self.device, dtype=torch.long)

            with torch.no_grad():
                # Conditional prediction
                cond_noise_pred = self.sampler.predict_noise(
                    actions, timestep_batch, conditions, use_dc=use_dc
                )

                # Unconditional prediction
                uncond_noise_pred = self.sampler.predict_noise(
                    actions, timestep_batch, uncond_conditions, use_dc=False
                )

                # Apply classifier-free guidance
                noise_pred = uncond_noise_pred + guidance_scale * (cond_noise_pred - uncond_noise_pred)

            # Update sample
            actions = self.scheduler.step(noise_pred, t.item(), actions)

        return actions


def create_transformer_dc_sampler(
    config: DiffusionConfig,
    cond_dim: int,
    n_dc_tokens: int = 4,
    n_dc_layers: int = 6,
    use_dc_t: bool = True,
    device: str = 'cuda',
    dtype: str = 'float32'
) -> TransformerDCSampler:
    """
    Factory function to create a TransformerDCSampler.

    Args:
        config: DiffusionConfig for the transformer
        cond_dim: dimension of conditioning information
        n_dc_tokens: number of DC tokens per layer
        n_dc_layers: number of layers to apply DC tokens
        use_dc_t: whether to use time-dependent DC tokens
        device: device to run on
        dtype: data type

    Returns:
        Configured TransformerDCSampler
    """
    sampler_config = TransformerDCSamplerConfig(
        n_dc_tokens=n_dc_tokens,
        n_dc_layers=n_dc_layers,
        use_dc_t=use_dc_t,
        device=device,
        dtype=dtype
    )

    return TransformerDCSampler(config, sampler_config, cond_dim)
