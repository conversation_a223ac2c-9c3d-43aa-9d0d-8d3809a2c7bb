"""
Training script for DiffusionModel_DC with SoftREPA-style contrastive learning.
Adapted for robot action diffusion tasks.
"""

import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from torch.optim import Optimizer
import numpy as np
from typing import Dict, Any, Optional, Tuple
import logging
import time
from contextlib import nullcontext
from tqdm import tqdm
import wandb
from pathlib import Path

# Import lerobot's standard components
from lerobot.common.datasets.factory import make_dataset
from lerobot.common.datasets.sampler import EpisodeAwareSampler
from lerobot.common.datasets.utils import cycle
from lerobot.common.policies.utils import get_device_from_parameters
from lerobot.common.utils.logging_utils import AverageMeter, MetricsTracker
from lerobot.common.utils.random_utils import set_seed
from lerobot.common.utils.utils import (
    format_big_number,
    get_safe_torch_device,
    has_method,
)

from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss,
    SoftREPATrainer,
    TransformerDCInference,
    create_diffusion_model_dc
)


class TransformerDCTrainingConfig:
    """Configuration for TransformerDC training"""
    def __init__(self):
        # Model config
        self.n_dc_tokens: int = 4
        self.n_dc_layers: int = 6
        self.use_dc_t: bool = True
        self.pretrained_model_path: Optional[str] = None  # Path to pretrained DiffusionModel

        # Training config
        self.batch_size: int = 64
        self.learning_rate: float = 1e-4
        self.weight_decay: float = 1e-3
        self.epochs: int = 100
        self.warmup_steps: int = 1000
        
        # Contrastive loss config
        self.temp: float = 0.07
        self.scale: float = 4.0
        self.dweight: float = 0.0  # diffusion loss weight
        
        # Training settings
        self.use_amp: bool = True  # automatic mixed precision
        self.gradient_clip_norm: float = 1.0
        self.save_freq: int = 10  # save every N epochs
        self.eval_freq: int = 5   # evaluate every N epochs
        
        # Device settings
        self.device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.dtype: str = 'float16' if self.use_amp else 'float32'
        
        # Logging
        self.use_wandb: bool = False
        self.log_freq: int = 100  # log every N steps


class TransformerDCTrainer:
    """
    Trainer for DiffusionModel_DC with SoftREPA-style contrastive learning.
    """
    
    def __init__(
        self,
        diffusion_config: DiffusionConfig,
        training_config: TransformerDCTrainingConfig,
        cond_dim: int,
        save_dir: str = "./checkpoints"
    ):
        self.diffusion_config = diffusion_config
        self.training_config = training_config
        self.cond_dim = cond_dim
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Create diffusion model with DC tokens
        if training_config.pretrained_model_path:
            self.logger.info(f"Loading from pretrained model: {training_config.pretrained_model_path}")
        else:
            self.logger.info("Creating model from scratch (no pretrained weights)")

        self.diffusion_model = create_diffusion_model_dc(
            config=diffusion_config,
            n_dc_tokens=training_config.n_dc_tokens,
            n_dc_layers=training_config.n_dc_layers,
            use_dc_t=training_config.use_dc_t,
            pretrained_model_path=training_config.pretrained_model_path
        )

        # Create trainer and loss
        self.softrepa_trainer = SoftREPATrainer(self.diffusion_model)
        self.contrastive_loss = ContrastiveLoss(
            temp=training_config.temp,
            scale=training_config.scale,
            dweight=training_config.dweight,
            device=training_config.device
        )

        # Setup optimizer
        self._setup_optimizer()

        # Setup mixed precision
        self.scaler = GradScaler() if training_config.use_amp else None

        # Initialize inference engine
        self.inference = TransformerDCInference(self.diffusion_model)
        
        # Training state
        self.current_epoch = 0
        self.global_step = 0
        self.best_loss = float('inf')
        
    def _setup_optimizer(self):
        """Setup optimizer with only DC token parameters"""
        # Freeze base model, only train DC tokens
        self.diffusion_model.freeze_base_model()

        # Get trainable parameters
        trainable_params = self.diffusion_model.get_dc_parameters()

        if not trainable_params:
            raise ValueError("No trainable parameters found! Check DC token setup.")

        self.logger.info(f"Training {len(trainable_params)} DC token parameters")
        for param in trainable_params:
            self.logger.info(f"  - DC parameter: {param.numel()} params")

        # Print detailed parameter statistics
        self.diffusion_model.print_parameter_stats()

        # Create optimizer
        self.optimizer = torch.optim.AdamW(
            trainable_params,
            lr=self.training_config.learning_rate,
            weight_decay=self.training_config.weight_decay,
            betas=(0.9, 0.999)
        )

        # Learning rate scheduler
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=10, T_mult=1, eta_min=1e-6
        )
    
    def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        Single training step with contrastive learning.

        Args:
            batch: Dictionary containing action and observation data

        Returns:
            Dictionary of loss values
        """
        # Move batch to device
        batch = {k: v.to(self.training_config.device) for k, v in batch.items()}
        batch_size = batch['action'].shape[0]

        # Forward pass with mixed precision
        if self.training_config.use_amp:
            with autocast():
                # Compute contrastive error matrix
                error_matrix = self.softrepa_trainer(batch, use_dc=True)

                # Compute contrastive loss
                loss = self.contrastive_loss(error_matrix)
        else:
            error_matrix = self.softrepa_trainer(batch, use_dc=True)
            loss = self.contrastive_loss(error_matrix)

        # Backward pass
        self.optimizer.zero_grad()

        if self.training_config.use_amp:
            self.scaler.scale(loss).backward()

            # Gradient clipping
            if self.training_config.gradient_clip_norm > 0:
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(
                    self.diffusion_model.get_dc_parameters(),
                    self.training_config.gradient_clip_norm
                )

            self.scaler.step(self.optimizer)
            self.scaler.update()
        else:
            loss.backward()

            # Gradient clipping
            if self.training_config.gradient_clip_norm > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.diffusion_model.get_dc_parameters(),
                    self.training_config.gradient_clip_norm
                )

            self.optimizer.step()

        self.scheduler.step()

        # Compute metrics
        with torch.no_grad():
            # Diagonal accuracy (correct action-observation pairs)
            diagonal_errors = torch.diag(error_matrix)
            off_diagonal_errors = error_matrix[~torch.eye(batch_size, dtype=bool, device=error_matrix.device)]

            accuracy = (diagonal_errors.mean() < off_diagonal_errors.mean()).float()

        return {
            'loss': loss.item(),
            'diagonal_error': diagonal_errors.mean().item(),
            'off_diagonal_error': off_diagonal_errors.mean().item(),
            'accuracy': accuracy.item(),
            'lr': self.scheduler.get_last_lr()[0]
        }
    
    def evaluate(self, val_dataloader: DataLoader) -> Dict[str, float]:
        """Evaluate model on validation set"""
        self.diffusion_model.eval()
        total_loss = 0.0
        total_accuracy = 0.0
        num_batches = 0

        with torch.no_grad():
            for batch in tqdm(val_dataloader, desc="Evaluating"):
                # Move batch to device
                batch = {k: v.to(self.training_config.device) for k, v in batch.items()}
                batch_size = batch['action'].shape[0]

                # Forward pass
                error_matrix = self.softrepa_trainer(batch, use_dc=True)
                loss = self.contrastive_loss(error_matrix)

                # Compute accuracy
                diagonal_errors = torch.diag(error_matrix)
                off_diagonal_errors = error_matrix[~torch.eye(batch_size, dtype=bool, device=error_matrix.device)]
                accuracy = (diagonal_errors.mean() < off_diagonal_errors.mean()).float()

                total_loss += loss.item()
                total_accuracy += accuracy.item()
                num_batches += 1

        self.diffusion_model.train()

        return {
            'val_loss': total_loss / num_batches,
            'val_accuracy': total_accuracy / num_batches
        }

    def test_inference(self, test_batch: Dict[str, torch.Tensor], num_samples: int = 4) -> Dict[str, torch.Tensor]:
        """Test inference functionality and compare DC vs non-DC results"""
        self.diffusion_model.eval()

        # Take only first few samples for testing
        test_batch_small = {k: v[:num_samples] for k, v in test_batch.items()}

        with torch.no_grad():
            # Sample without DC tokens
            actions_no_dc = self.inference.sample(
                batch=test_batch_small,
                use_dc=False
            )

            # Sample with DC tokens
            actions_with_dc = self.inference.sample(
                batch=test_batch_small,
                use_dc=True
            )

            # Sample with guidance
            actions_guided = self.inference.sample_with_guidance(
                batch=test_batch_small,
                guidance_scale=2.0,
                use_dc=True
            )

        self.diffusion_model.train()

        return {
            'actions_no_dc': actions_no_dc,
            'actions_with_dc': actions_with_dc,
            'actions_guided': actions_guided,
            'original_actions': test_batch_small['action']
        }
    
    def train(
        self, 
        train_dataloader: DataLoader, 
        val_dataloader: Optional[DataLoader] = None
    ):
        """
        Main training loop.
        
        Args:
            train_dataloader: Training data loader
            val_dataloader: Validation data loader (optional)
        """
        if self.training_config.use_wandb:
            wandb.init(project="transformer-dc-diffusion", config=self.training_config.__dict__)
        
        self.logger.info("Starting training...")
        self.logger.info(f"Device: {self.training_config.device}")
        self.logger.info(f"Batch size: {self.training_config.batch_size}")
        self.logger.info(f"Learning rate: {self.training_config.learning_rate}")
        
        for epoch in range(self.current_epoch, self.training_config.epochs):
            self.current_epoch = epoch
            epoch_losses = []
            
            # Training loop
            pbar = tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{self.training_config.epochs}")
            for batch in enumerate(pbar):
                metrics = self.train_step(batch)
                epoch_losses.append(metrics['loss'])
                
                # Update progress bar
                pbar.set_postfix({
                    'loss': f"{metrics['loss']:.4f}",
                    'acc': f"{metrics['accuracy']:.3f}",
                    'lr': f"{metrics['lr']:.2e}"
                })
                
                # Logging
                if self.global_step % self.training_config.log_freq == 0:
                    if self.training_config.use_wandb:
                        wandb.log(metrics, step=self.global_step)
                    
                    self.logger.info(
                        f"Step {self.global_step}: Loss={metrics['loss']:.4f}, "
                        f"Acc={metrics['accuracy']:.3f}, LR={metrics['lr']:.2e}"
                    )
                
                self.global_step += 1
            
            # Epoch summary
            avg_loss = np.mean(epoch_losses)
            self.logger.info(f"Epoch {epoch+1} completed. Average loss: {avg_loss:.4f}")
            
            # Validation
            if val_dataloader is not None and (epoch + 1) % self.training_config.eval_freq == 0:
                val_metrics = self.evaluate(val_dataloader)
                self.logger.info(f"Validation: Loss={val_metrics['val_loss']:.4f}, "
                               f"Acc={val_metrics['val_accuracy']:.3f}")

                # # Test inference every few epochs
                # if (epoch + 1) % (self.training_config.eval_freq * 2) == 0:
                #     # Get a test batch from validation data
                #     test_batch = next(iter(val_dataloader))
                #     test_batch = {k: v.to(self.training_config.device) for k, v in test_batch.items()}

                #     inference_results = self.test_inference(test_batch)

                #     # Log inference statistics
                #     self.logger.info("Inference Test Results:")
                #     for key, actions in inference_results.items():
                #         if 'actions' in key:
                #             self.logger.info(f"  {key}: mean={actions.mean():.4f}, std={actions.std():.4f}")

                #     # Log to wandb if enabled
                #     if self.training_config.use_wandb:
                #         inference_stats = {}
                #         for key, actions in inference_results.items():
                #             if 'actions' in key:
                #                 inference_stats[f"inference/{key}_mean"] = actions.mean().item()
                #                 inference_stats[f"inference/{key}_std"] = actions.std().item()
                #         wandb.log(inference_stats, step=self.global_step)

                if self.training_config.use_wandb:
                    wandb.log(val_metrics, step=self.global_step)

                # Save best model
                if val_metrics['val_loss'] < self.best_loss:
                    self.best_loss = val_metrics['val_loss']
                    self.save_checkpoint(is_best=True)
            
            # Regular checkpoint saving
            if (epoch + 1) % self.training_config.save_freq == 0:
                self.save_checkpoint()
        
        self.logger.info("Training completed!")
        
        if self.training_config.use_wandb:
            wandb.finish()
    
    def save_checkpoint(self, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': self.current_epoch,
            'global_step': self.global_step,
            'model_state_dict': self.diffusion_model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_loss': self.best_loss,
            'config': {
                'diffusion_config': self.diffusion_config,
                'training_config': self.training_config.__dict__,
                'cond_dim': self.cond_dim
            }
        }

        if self.training_config.use_amp:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()

        # Save regular checkpoint
        checkpoint_path = self.save_dir / f"checkpoint_epoch_{self.current_epoch+1}.pt"
        torch.save(checkpoint, checkpoint_path)

        # Save best checkpoint
        if is_best:
            best_path = self.save_dir / "best_checkpoint.pt"
            torch.save(checkpoint, best_path)
            self.logger.info(f"Best model saved to {best_path}")

        self.logger.info(f"Checkpoint saved to {checkpoint_path}")

    def load_checkpoint(self, checkpoint_path: str):
        """Load model checkpoint"""
        checkpoint = torch.load(checkpoint_path, map_location=self.training_config.device)

        self.diffusion_model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        self.current_epoch = checkpoint['epoch']
        self.global_step = checkpoint['global_step']
        self.best_loss = checkpoint['best_loss']

        if self.training_config.use_amp and 'scaler_state_dict' in checkpoint:
            self.scaler.load_state_dict(checkpoint['scaler_state_dict'])

        self.logger.info(f"Checkpoint loaded from {checkpoint_path}")
        self.logger.info(f"Resuming from epoch {self.current_epoch+1}, step {self.global_step}")


def create_trainer(
    diffusion_config: DiffusionConfig,
    cond_dim: int,
    training_config: Optional[TransformerDCTrainingConfig] = None,
    save_dir: str = "./checkpoints"
) -> TransformerDCTrainer:
    """
    Factory function to create a TransformerDCTrainer.
    
    Args:
        diffusion_config: Configuration for the diffusion model
        cond_dim: Dimension of conditioning information
        training_config: Training configuration (uses default if None)
        save_dir: Directory to save checkpoints
        
    Returns:
        Configured TransformerDCTrainer
    """
    if training_config is None:
        training_config = TransformerDCTrainingConfig()
    
    return TransformerDCTrainer(
        diffusion_config=diffusion_config,
        training_config=training_config,
        cond_dim=cond_dim,
        save_dir=save_dir
    )

'''========================================================================================================================================================================'''

def update_policy_dc(
    train_metrics: MetricsTracker,
    diffusion_model: DiffusionModel_DC,
    trainer: SoftREPATrainer,
    contrastive_loss: ContrastiveLoss,
    batch: Dict[str, torch.Tensor],
    optimizer: torch.optim.Optimizer,
    grad_clip_norm: float,
    grad_scaler: GradScaler,
    lr_scheduler=None,
    use_amp: bool = False,
) -> tuple[MetricsTracker, dict]:
    """
    Update policy with contrastive learning.
    Based on lerobot's standard update_policy function but adapted for contrastive learning.
    """
    start_time = time.perf_counter()
    device = get_device_from_parameters(diffusion_model)
    diffusion_model.train()

    # Forward pass with mixed precision
    with torch.autocast(device_type=device.type) if use_amp else nullcontext():
        # Compute contrastive error matrix
        error_matrix = trainer(batch, use_dc=True)

        # Compute contrastive loss
        loss = contrastive_loss(error_matrix)

    # Backward pass
    grad_scaler.scale(loss).backward()

    # Unscale gradients for clipping
    grad_scaler.unscale_(optimizer)

    # Gradient clipping
    grad_norm = torch.nn.utils.clip_grad_norm_(
        diffusion_model.get_dc_parameters(),
        grad_clip_norm,
        error_if_nonfinite=False,
    )

    # Step optimizer with scaler
    grad_scaler.step(optimizer)
    grad_scaler.update()

    optimizer.zero_grad()

    # Step scheduler if provided
    if lr_scheduler is not None:
        lr_scheduler.step()

    # Update metrics
    train_metrics.loss = loss.item()
    train_metrics.grad_norm = grad_norm.item()
    train_metrics.lr = optimizer.param_groups[0]["lr"]
    train_metrics.update_s = time.perf_counter() - start_time

    # Compute additional metrics for output dict
    with torch.no_grad():
        batch_size = error_matrix.shape[0]
        diagonal_errors = torch.diag(error_matrix)
        off_diagonal_errors = error_matrix[~torch.eye(batch_size, dtype=bool, device=error_matrix.device)]

        accuracy = (diagonal_errors.mean() < off_diagonal_errors.mean()).float()

        output_dict = {
            'diagonal_error': diagonal_errors.mean().item(),
            'off_diagonal_error': off_diagonal_errors.mean().item(),
            'accuracy': accuracy.item(),
        }

    return train_metrics, output_dict


def train_dc_policy(cfg, dataset_name: str = "pusht", pretrained_model_path: Optional[str] = None):
    """
    Train DiffusionModel_DC using lerobot's standard training pipeline.

    Args:
        cfg: Training configuration (similar to lerobot's TrainPipelineConfig)
        dataset_name: Name of the dataset to use
        pretrained_model_path: Path to pretrained DiffusionModel checkpoint
    """
    # Initialize logging
    init_logging()
    logging.info("Starting DiffusionModel_DC training")

    # Set seed for reproducibility
    if hasattr(cfg, 'seed') and cfg.seed is not None:
        set_seed(cfg.seed)

    # Check device availability
    device = get_safe_torch_device(cfg.device, log=True)
    torch.backends.cudnn.benchmark = True
    torch.backends.cuda.matmul.allow_tf32 = True

    # Create dataset using lerobot's factory
    logging.info("Creating dataset")
    with tqdm(desc="Loading dataset", unit="step", leave=False) as dataset_pbar:
        dataset_pbar.set_description("Loading dataset metadata")
        dataset = make_dataset(cfg)
        dataset_pbar.set_description("Dataset loaded successfully")
        dataset_pbar.update(1)
        dataset_pbar.close()

    # Create DiffusionModel_DC with pretrained weights
    logging.info("Creating DiffusionModel_DC")
    diffusion_model = create_diffusion_model_dc(
        config=cfg.policy,
        n_dc_tokens=getattr(cfg, 'n_dc_tokens', 4),
        n_dc_layers=getattr(cfg, 'n_dc_layers', 6),
        use_dc_t=getattr(cfg, 'use_dc_t', True),
        pretrained_model_path=pretrained_model_path
    )

    # Move to device
    diffusion_model.to(device)

    # Freeze base model and get DC parameters
    diffusion_model.freeze_base_model()
    dc_params = diffusion_model.get_dc_parameters()

    # Print parameter statistics
    diffusion_model.print_parameter_stats()

    # Create optimizer for DC parameters only
    optimizer = torch.optim.AdamW(
        dc_params,
        lr=getattr(cfg, 'learning_rate', 1e-4),
        weight_decay=getattr(cfg, 'weight_decay', 1e-3),
        betas=(0.9, 0.999)
    )

    # Create scheduler
    lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=1, eta_min=1e-6
    )

    # Create gradient scaler
    grad_scaler = GradScaler(device.type, enabled=getattr(cfg, 'use_amp', True))

    # Create contrastive learning components
    trainer = SoftREPATrainer(diffusion_model)
    contrastive_loss = ContrastiveLoss(
        temp=getattr(cfg, 'temp', 0.07),
        scale=getattr(cfg, 'scale', 4.0),
        device=device.type
    )

    # Create dataloader
    if hasattr(cfg, "drop_n_last_frames") and cfg.drop_n_last_frames:
        shuffle = False
        sampler = EpisodeAwareSampler(
            dataset.episode_data_index,
            drop_n_last_frames=cfg.drop_n_last_frames,
            shuffle=True,
        )
    else:
        shuffle = True
        sampler = None

    dataloader = torch.utils.data.DataLoader(
        dataset,
        num_workers=getattr(cfg, 'num_workers', 4),
        batch_size=getattr(cfg, 'batch_size', 64),
        shuffle=shuffle,
        sampler=sampler,
        pin_memory=device.type != "cpu",
        drop_last=False,
    )
    dl_iter = cycle(dataloader)

    # Create metrics tracker
    train_metrics = {
        "loss": AverageMeter("loss", ":.3f"),
        "grad_norm": AverageMeter("grdn", ":.3f"),
        "lr": AverageMeter("lr", ":0.1e"),
        "update_s": AverageMeter("updt_s", ":.3f"),
        "dataloading_s": AverageMeter("data_s", ":.3f"),
        "diagonal_error": AverageMeter("diag_err", ":.4f"),
        "off_diagonal_error": AverageMeter("off_diag_err", ":.4f"),
        "accuracy": AverageMeter("acc", ":.3f"),
    }

    train_tracker = MetricsTracker(
        getattr(cfg, 'batch_size', 64),
        dataset.num_frames,
        dataset.num_episodes,
        train_metrics,
        initial_step=0
    )

    # Training loop
    logging.info("Start contrastive training")
    steps = getattr(cfg, 'steps', 10000)
    log_freq = getattr(cfg, 'log_freq', 100)

    progress_bar = tqdm(
        range(steps),
        desc="Training DC tokens",
        unit="step",
        dynamic_ncols=True,
        leave=True,
    )

    for step in progress_bar:
        start_time = time.perf_counter()
        batch = next(dl_iter)
        train_tracker.dataloading_s = time.perf_counter() - start_time

        # Move batch to device
        for key in batch:
            if isinstance(batch[key], torch.Tensor):
                batch[key] = batch[key].to(device, non_blocking=True)

        # Update policy with contrastive learning
        train_tracker, output_dict = update_policy_dc(
            train_tracker,
            diffusion_model,
            trainer,
            contrastive_loss,
            batch,
            optimizer,
            getattr(cfg, 'grad_clip_norm', 1.0),
            grad_scaler=grad_scaler,
            lr_scheduler=lr_scheduler,
            use_amp=getattr(cfg, 'use_amp', True),
        )

        # Update additional metrics
        train_tracker.diagonal_error = output_dict['diagonal_error']
        train_tracker.off_diagonal_error = output_dict['off_diagonal_error']
        train_tracker.accuracy = output_dict['accuracy']

        train_tracker.step()

        # Logging
        if log_freq > 0 and (step + 1) % log_freq == 0:
            postfix_dict = {
                'loss': f"{train_tracker.loss.avg:.3f}",
                'acc': f"{train_tracker.accuracy.avg:.3f}",
                'lr': f"{train_tracker.lr.avg:.1e}",
            }
            progress_bar.set_postfix(postfix_dict)

            logging.info(
                f"Step {step+1}/{steps}: "
                f"Loss={train_tracker.loss.avg:.4f}, "
                f"Acc={train_tracker.accuracy.avg:.3f}, "
                f"DiagErr={train_tracker.diagonal_error.avg:.4f}, "
                f"OffDiagErr={train_tracker.off_diagonal_error.avg:.4f}"
            )

    logging.info("Training completed!")
    return diffusion_model
