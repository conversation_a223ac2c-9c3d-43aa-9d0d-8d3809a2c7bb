"""
Training script for TransformerForDiffusion_DC with SoftREPA-style contrastive learning.
Adapted for robot action diffusion tasks.
"""

import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
import numpy as np
from typing import Dict, Any, Optional, Tuple
import logging
from tqdm import tqdm
import wandb
from pathlib import Path

from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss,
    SoftREPATrainer,
    TransformerDCInference,
    create_diffusion_model_dc
)


class TransformerDCTrainingConfig:
    """Configuration for TransformerDC training"""
    def __init__(self):
        # Model config
        self.n_dc_tokens: int = 4
        self.n_dc_layers: int = 6
        self.use_dc_t: bool = True
        
        # Training config
        self.batch_size: int = 64
        self.learning_rate: float = 1e-4
        self.weight_decay: float = 1e-3
        self.epochs: int = 100
        self.warmup_steps: int = 1000
        
        # Contrastive loss config
        self.temp: float = 0.07
        self.scale: float = 4.0
        self.dweight: float = 0.0  # diffusion loss weight
        
        # Training settings
        self.use_amp: bool = True  # automatic mixed precision
        self.gradient_clip_norm: float = 1.0
        self.save_freq: int = 10  # save every N epochs
        self.eval_freq: int = 5   # evaluate every N epochs
        
        # Device settings
        self.device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.dtype: str = 'float16' if self.use_amp else 'float32'
        
        # Logging
        self.use_wandb: bool = False
        self.log_freq: int = 100  # log every N steps


class TransformerDCTrainer:
    """
    Trainer for TransformerForDiffusion_DC with SoftREPA-style contrastive learning.
    """
    
    def __init__(
        self,
        diffusion_config: DiffusionConfig,
        training_config: TransformerDCTrainingConfig,
        cond_dim: int,
        save_dir: str = "./checkpoints"
    ):
        self.diffusion_config = diffusion_config
        self.training_config = training_config
        self.cond_dim = cond_dim
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Create diffusion model with DC tokens
        self.diffusion_model = create_diffusion_model_dc(
            config=diffusion_config,
            n_dc_tokens=training_config.n_dc_tokens,
            n_dc_layers=training_config.n_dc_layers,
            use_dc_t=training_config.use_dc_t
        )

        # Create trainer and loss
        self.softrepa_trainer = SoftREPATrainer(self.diffusion_model)
        self.contrastive_loss = ContrastiveLoss(
            temp=training_config.temp,
            scale=training_config.scale,
            dweight=training_config.dweight,
            device=training_config.device
        )

        # Setup optimizer
        self._setup_optimizer()

        # Setup mixed precision
        self.scaler = GradScaler() if training_config.use_amp else None

        # Initialize inference engine
        self.inference = TransformerDCInference(self.diffusion_model)
        
        # Training state
        self.current_epoch = 0
        self.global_step = 0
        self.best_loss = float('inf')
        
    def _setup_optimizer(self):
        """Setup optimizer with only DC token parameters"""
        # Freeze base model, only train DC tokens
        self.diffusion_model.freeze_base_model()

        # Get trainable parameters
        trainable_params = self.diffusion_model.get_dc_parameters()

        if not trainable_params:
            raise ValueError("No trainable parameters found! Check DC token setup.")

        self.logger.info(f"Training {len(trainable_params)} DC token parameters")
        for param in trainable_params:
            self.logger.info(f"  - DC parameter: {param.numel()} params")

        # Create optimizer
        self.optimizer = torch.optim.AdamW(
            trainable_params,
            lr=self.training_config.learning_rate,
            weight_decay=self.training_config.weight_decay,
            betas=(0.9, 0.999)
        )

        # Learning rate scheduler
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=10, T_mult=1, eta_min=1e-6
        )
    
    def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        Single training step with contrastive learning.

        Args:
            batch: Dictionary containing action and observation data

        Returns:
            Dictionary of loss values
        """
        # Move batch to device
        batch = {k: v.to(self.training_config.device) for k, v in batch.items()}
        batch_size = batch['action'].shape[0]

        # Forward pass with mixed precision
        if self.training_config.use_amp:
            with autocast():
                # Compute contrastive error matrix
                error_matrix = self.softrepa_trainer(batch, use_dc=True)

                # Compute contrastive loss
                loss = self.contrastive_loss(error_matrix)
        else:
            error_matrix = self.softrepa_trainer(batch, use_dc=True)
            loss = self.contrastive_loss(error_matrix)

        # Backward pass
        self.optimizer.zero_grad()

        if self.training_config.use_amp:
            self.scaler.scale(loss).backward()

            # Gradient clipping
            if self.training_config.gradient_clip_norm > 0:
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(
                    self.diffusion_model.get_dc_parameters(),
                    self.training_config.gradient_clip_norm
                )

            self.scaler.step(self.optimizer)
            self.scaler.update()
        else:
            loss.backward()

            # Gradient clipping
            if self.training_config.gradient_clip_norm > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.diffusion_model.get_dc_parameters(),
                    self.training_config.gradient_clip_norm
                )

            self.optimizer.step()

        self.scheduler.step()

        # Compute metrics
        with torch.no_grad():
            # Diagonal accuracy (correct action-observation pairs)
            diagonal_errors = torch.diag(error_matrix)
            off_diagonal_errors = error_matrix[~torch.eye(batch_size, dtype=bool, device=error_matrix.device)]

            accuracy = (diagonal_errors.mean() < off_diagonal_errors.mean()).float()

        return {
            'loss': loss.item(),
            'diagonal_error': diagonal_errors.mean().item(),
            'off_diagonal_error': off_diagonal_errors.mean().item(),
            'accuracy': accuracy.item(),
            'lr': self.scheduler.get_last_lr()[0]
        }
    
    def evaluate(self, val_dataloader: DataLoader) -> Dict[str, float]:
        """Evaluate model on validation set"""
        self.diffusion_model.eval()
        total_loss = 0.0
        total_accuracy = 0.0
        num_batches = 0

        with torch.no_grad():
            for batch in tqdm(val_dataloader, desc="Evaluating"):
                # Move batch to device
                batch = {k: v.to(self.training_config.device) for k, v in batch.items()}
                batch_size = batch['action'].shape[0]

                # Forward pass
                error_matrix = self.softrepa_trainer(batch, use_dc=True)
                loss = self.contrastive_loss(error_matrix)

                # Compute accuracy
                diagonal_errors = torch.diag(error_matrix)
                off_diagonal_errors = error_matrix[~torch.eye(batch_size, dtype=bool, device=error_matrix.device)]
                accuracy = (diagonal_errors.mean() < off_diagonal_errors.mean()).float()

                total_loss += loss.item()
                total_accuracy += accuracy.item()
                num_batches += 1

        self.diffusion_model.train()

        return {
            'val_loss': total_loss / num_batches,
            'val_accuracy': total_accuracy / num_batches
        }
    
    def train(
        self, 
        train_dataloader: DataLoader, 
        val_dataloader: Optional[DataLoader] = None
    ):
        """
        Main training loop.
        
        Args:
            train_dataloader: Training data loader
            val_dataloader: Validation data loader (optional)
        """
        if self.training_config.use_wandb:
            wandb.init(project="transformer-dc-diffusion", config=self.training_config.__dict__)
        
        self.logger.info("Starting training...")
        self.logger.info(f"Device: {self.training_config.device}")
        self.logger.info(f"Batch size: {self.training_config.batch_size}")
        self.logger.info(f"Learning rate: {self.training_config.learning_rate}")
        
        for epoch in range(self.current_epoch, self.training_config.epochs):
            self.current_epoch = epoch
            epoch_losses = []
            
            # Training loop
            pbar = tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{self.training_config.epochs}")
            for batch in enumerate(pbar):
                metrics = self.train_step(batch)
                epoch_losses.append(metrics['loss'])
                
                # Update progress bar
                pbar.set_postfix({
                    'loss': f"{metrics['loss']:.4f}",
                    'acc': f"{metrics['accuracy']:.3f}",
                    'lr': f"{metrics['lr']:.2e}"
                })
                
                # Logging
                if self.global_step % self.training_config.log_freq == 0:
                    if self.training_config.use_wandb:
                        wandb.log(metrics, step=self.global_step)
                    
                    self.logger.info(
                        f"Step {self.global_step}: Loss={metrics['loss']:.4f}, "
                        f"Acc={metrics['accuracy']:.3f}, LR={metrics['lr']:.2e}"
                    )
                
                self.global_step += 1
            
            # Epoch summary
            avg_loss = np.mean(epoch_losses)
            self.logger.info(f"Epoch {epoch+1} completed. Average loss: {avg_loss:.4f}")
            
            # Validation
            if val_dataloader is not None and (epoch + 1) % self.training_config.eval_freq == 0:
                val_metrics = self.evaluate(val_dataloader)
                self.logger.info(f"Validation: Loss={val_metrics['val_loss']:.4f}, "
                               f"Acc={val_metrics['val_accuracy']:.3f}")
                
                if self.training_config.use_wandb:
                    wandb.log(val_metrics, step=self.global_step)
                
                # Save best model
                if val_metrics['val_loss'] < self.best_loss:
                    self.best_loss = val_metrics['val_loss']
                    self.save_checkpoint(is_best=True)
            
            # Regular checkpoint saving
            if (epoch + 1) % self.training_config.save_freq == 0:
                self.save_checkpoint()
        
        self.logger.info("Training completed!")
        
        if self.training_config.use_wandb:
            wandb.finish()
    
    def save_checkpoint(self, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': self.current_epoch,
            'global_step': self.global_step,
            'model_state_dict': self.diffusion_model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_loss': self.best_loss,
            'config': {
                'diffusion_config': self.diffusion_config,
                'training_config': self.training_config.__dict__,
                'cond_dim': self.cond_dim
            }
        }

        if self.training_config.use_amp:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()

        # Save regular checkpoint
        checkpoint_path = self.save_dir / f"checkpoint_epoch_{self.current_epoch+1}.pt"
        torch.save(checkpoint, checkpoint_path)

        # Save best checkpoint
        if is_best:
            best_path = self.save_dir / "best_checkpoint.pt"
            torch.save(checkpoint, best_path)
            self.logger.info(f"Best model saved to {best_path}")

        self.logger.info(f"Checkpoint saved to {checkpoint_path}")

    def load_checkpoint(self, checkpoint_path: str):
        """Load model checkpoint"""
        checkpoint = torch.load(checkpoint_path, map_location=self.training_config.device)

        self.diffusion_model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        self.current_epoch = checkpoint['epoch']
        self.global_step = checkpoint['global_step']
        self.best_loss = checkpoint['best_loss']

        if self.training_config.use_amp and 'scaler_state_dict' in checkpoint:
            self.scaler.load_state_dict(checkpoint['scaler_state_dict'])

        self.logger.info(f"Checkpoint loaded from {checkpoint_path}")
        self.logger.info(f"Resuming from epoch {self.current_epoch+1}, step {self.global_step}")


def create_trainer(
    diffusion_config: DiffusionConfig,
    cond_dim: int,
    training_config: Optional[TransformerDCTrainingConfig] = None,
    save_dir: str = "./checkpoints"
) -> TransformerDCTrainer:
    """
    Factory function to create a TransformerDCTrainer.
    
    Args:
        diffusion_config: Configuration for the diffusion model
        cond_dim: Dimension of conditioning information
        training_config: Training configuration (uses default if None)
        save_dir: Directory to save checkpoints
        
    Returns:
        Configured TransformerDCTrainer
    """
    if training_config is None:
        training_config = TransformerDCTrainingConfig()
    
    return TransformerDCTrainer(
        diffusion_config=diffusion_config,
        training_config=training_config,
        cond_dim=cond_dim,
        save_dir=save_dir
    )
