"""
Simple test script for TransformerForDiffusion_DC sampler.
This script verifies that the implementation works correctly.
"""

import torch
import sys
import os

# Add the lerobot path
sys.path.append('/home/<USER>/work/lerobot-add_transformer')

from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    create_transformer_dc_sampler,
    TransformerDCInference,
    SoftREPATrainer,
    ContrastiveLoss
)


def test_sampler_creation():
    """Test basic sampler creation"""
    print("Testing sampler creation...")
    
    # Create config
    config = DiffusionConfig(
        horizon=16,
        action_feature=torch.zeros(7),
        diffusion_step_embed_dim=128,
        n_layer=4,
        n_head=4,
        n_obs_steps=1,
        n_cond_layers=2,
        causal_attn=True,
        p_drop_emb=0.1,
        p_drop_attn=0.1
    )
    
    # Create sampler
    sampler = create_transformer_dc_sampler(
        config=config,
        cond_dim=64,
        n_dc_tokens=4,
        n_dc_layers=3,
        use_dc_t=True,
        device='cpu',  # Use CPU for testing
        dtype='float32'
    )
    
    print(f"✓ Sampler created successfully")
    print(f"  - Device: {sampler.device}")
    print(f"  - DC tokens shape: {sampler.denoiser.dc_tokens.shape}")
    print(f"  - Use DC-t: {sampler.denoiser.use_dc_t}")
    
    return sampler, config


def test_forward_pass(sampler, config):
    """Test forward pass through the model"""
    print("\nTesting forward pass...")
    
    batch_size = 4
    horizon = config.horizon
    action_dim = config.action_feature.shape[0]
    cond_dim = 64
    
    # Create test data
    actions = torch.randn(batch_size, horizon, action_dim)
    conditions = torch.randn(batch_size, cond_dim)
    timesteps = torch.randint(0, 1000, (batch_size,))
    
    # Test prediction without DC tokens
    sampler.denoiser.use_dc = False
    pred_no_dc = sampler.predict_noise(actions, timesteps, conditions, use_dc=False)
    
    # Test prediction with DC tokens
    sampler.denoiser.use_dc = True
    pred_with_dc = sampler.predict_noise(actions, timesteps, conditions, use_dc=True)
    
    print(f"✓ Forward pass successful")
    print(f"  - Input shape: {actions.shape}")
    print(f"  - Output shape (no DC): {pred_no_dc.shape}")
    print(f"  - Output shape (with DC): {pred_with_dc.shape}")
    print(f"  - Outputs different: {not torch.allclose(pred_no_dc, pred_with_dc, atol=1e-6)}")
    
    return pred_no_dc, pred_with_dc


def test_contrastive_training(sampler):
    """Test contrastive learning setup"""
    print("\nTesting contrastive training...")
    
    batch_size = 8
    horizon = 16
    action_dim = 7
    cond_dim = 64
    
    # Create test data
    actions = torch.randn(batch_size, horizon, action_dim)
    conditions = torch.randn(batch_size, cond_dim)
    timesteps = torch.randint(0, 1000, (batch_size,))
    
    # Create trainer and loss
    trainer = SoftREPATrainer(sampler)
    contrastive_loss = ContrastiveLoss(device='cpu')
    
    # Forward pass
    error_matrix = trainer(actions, conditions, timesteps, use_dc=True)
    loss = contrastive_loss(error_matrix)
    
    print(f"✓ Contrastive training successful")
    print(f"  - Error matrix shape: {error_matrix.shape}")
    print(f"  - Loss value: {loss.item():.4f}")
    print(f"  - Diagonal mean: {torch.diag(error_matrix).mean().item():.4f}")
    print(f"  - Off-diagonal mean: {error_matrix[~torch.eye(batch_size, dtype=bool)].mean().item():.4f}")
    
    return error_matrix, loss


def test_parameter_freezing(sampler):
    """Test that only DC parameters are trainable"""
    print("\nTesting parameter freezing...")
    
    # Freeze base model
    sampler.freeze_base_model()
    
    # Check trainable parameters
    trainable_params = sampler.get_trainable_parameters()
    all_params = list(sampler.named_parameters())
    
    dc_param_count = 0
    non_dc_param_count = 0
    
    for name, param in all_params:
        if 'dc' in name:
            dc_param_count += 1
            if not param.requires_grad:
                print(f"  ✗ DC parameter {name} is not trainable!")
        else:
            non_dc_param_count += 1
            if param.requires_grad:
                print(f"  ✗ Non-DC parameter {name} is trainable!")
    
    print(f"✓ Parameter freezing successful")
    print(f"  - Total parameters: {len(all_params)}")
    print(f"  - DC parameters: {dc_param_count}")
    print(f"  - Non-DC parameters: {non_dc_param_count}")
    print(f"  - Trainable parameters: {len(trainable_params)}")


def test_inference(sampler, config):
    """Test inference functionality"""
    print("\nTesting inference...")
    
    # Create inference engine
    inference = TransformerDCInference(sampler, num_inference_steps=10)  # Fewer steps for testing
    
    batch_size = 2
    horizon = config.horizon
    action_dim = config.action_feature.shape[0]
    cond_dim = 64
    
    # Create test conditions
    conditions = torch.randn(batch_size, cond_dim)
    
    # Test sampling without DC
    actions_no_dc = inference.sample(
        conditions=conditions,
        action_shape=(horizon, action_dim),
        use_dc=False
    )
    
    # Test sampling with DC
    actions_with_dc = inference.sample(
        conditions=conditions,
        action_shape=(horizon, action_dim),
        use_dc=True
    )
    
    # Test guided sampling
    actions_guided = inference.sample_with_guidance(
        conditions=conditions,
        action_shape=(horizon, action_dim),
        guidance_scale=1.5,
        use_dc=True
    )
    
    print(f"✓ Inference successful")
    print(f"  - Sample shape: {actions_no_dc.shape}")
    print(f"  - No DC mean/std: {actions_no_dc.mean():.4f} / {actions_no_dc.std():.4f}")
    print(f"  - With DC mean/std: {actions_with_dc.mean():.4f} / {actions_with_dc.std():.4f}")
    print(f"  - Guided mean/std: {actions_guided.mean():.4f} / {actions_guided.std():.4f}")
    
    return actions_no_dc, actions_with_dc, actions_guided


def test_gradient_flow(sampler):
    """Test that gradients flow correctly through DC tokens"""
    print("\nTesting gradient flow...")
    
    # Freeze base model
    sampler.freeze_base_model()
    
    batch_size = 4
    horizon = 16
    action_dim = 7
    cond_dim = 64
    
    # Create test data
    actions = torch.randn(batch_size, horizon, action_dim)
    conditions = torch.randn(batch_size, cond_dim)
    timesteps = torch.randint(0, 1000, (batch_size,))
    
    # Forward pass
    trainer = SoftREPATrainer(sampler)
    error_matrix = trainer(actions, conditions, timesteps, use_dc=True)
    
    # Compute loss
    contrastive_loss = ContrastiveLoss(device='cpu')
    loss = contrastive_loss(error_matrix)
    
    # Backward pass
    loss.backward()
    
    # Check gradients
    dc_grads_exist = False
    non_dc_grads_exist = False
    
    for name, param in sampler.named_parameters():
        if param.grad is not None:
            if 'dc' in name:
                dc_grads_exist = True
                print(f"  ✓ DC parameter {name} has gradient: {param.grad.norm().item():.6f}")
            else:
                non_dc_grads_exist = True
                print(f"  ✗ Non-DC parameter {name} has gradient!")
    
    print(f"✓ Gradient flow test {'passed' if dc_grads_exist and not non_dc_grads_exist else 'failed'}")
    print(f"  - DC gradients exist: {dc_grads_exist}")
    print(f"  - Non-DC gradients exist: {non_dc_grads_exist}")


def main():
    """Run all tests"""
    print("=" * 60)
    print("TransformerForDiffusion_DC Sampler Test Suite")
    print("=" * 60)
    
    try:
        # Test 1: Basic creation
        sampler, config = test_sampler_creation()
        
        # Test 2: Forward pass
        test_forward_pass(sampler, config)
        
        # Test 3: Contrastive training
        test_contrastive_training(sampler)
        
        # Test 4: Parameter freezing
        test_parameter_freezing(sampler)
        
        # Test 5: Inference
        test_inference(sampler, config)
        
        # Test 6: Gradient flow
        test_gradient_flow(sampler)
        
        print("\n" + "=" * 60)
        print("✓ All tests passed successfully!")
        print("The TransformerForDiffusion_DC sampler is working correctly.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
